<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core" xmlns:salesforce="http://www.mulesoft.org/schema/mule/salesforce"
	xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/salesforce http://www.mulesoft.org/schema/mule/salesforce/current/mule-salesforce.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd">
	<flow name="pf-mktplace-to-salesforce" doc:id="eaa674da-b0c7-4f03-b0df-55c04313a677" >
		<logger level="INFO" doc:name="LOG INFO: Log Entry" doc:id="3c143d08-bea2-4bdc-bea9-896de573fff2" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Market place purchaseData process flow started", &#10;	"FlowName" : "pf-mktplace-to-salesforce", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.vBusinessKey	&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Inbound Request" doc:id="5575f5a7-f9f6-4e9f-af4d-4c63aafe4904" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Inbound Request",&#10;	"FlowName" : "pf-mktplace-to-salesforce",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/purchaseData",&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		<logger level="DEBUG" doc:name="LOG DEBUG: Inbound Request Payload" doc:id="54efc0db-d385-478b-8c30-1e57aef073ff" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Log Inbound Request Payload", &#10;	"FlowName" : "pf-mktplace-to-salesforce",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/purchaseData",&#10;	"BackendRequest": payload,&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		<http:request method="POST" doc:name="Create entry in TRANSACTION_DETAILS" doc:id="b5e66de1-9d0f-40f8-9192-4a08d5df8464" config-ref="HTTPS_Request_DB_SYS_API" path="${https.request.dbSysApi.transactionDetails.path}" target="vTransactionResponse">
			<http:body><![CDATA[#[%dw 2.0
output application/json
---
{
  "transaction": {
    "CORRELATION_ID": vars.vCorrelationId,
    "OPERATION": "CREATE",
    "SOURCE": "MARKETPLACE",
    "STATUS": "STARTED",
    "LAST_UPDATED_BY": "PROCESS_API",
    "ENTERPRISE_ID":  "",
    "PAYLOAD": write(payload,'application/json'),
    "RECORD_ID": "",
    "OBJECT_TYPE": "DEAL",
    "PRIORITY": ("0") as Number,
    "RETRY_COUNT": 0,
    "QUERY_PARAMS": null,
    "ERROR_MSG": "",
    "ERROR_TYPE": "",
    "USER": null,
    "RECORD_NAME": null
  }
}]]]></http:body>
		</http:request>
		<salesforce:invoke-apex-rest-method doc:name="Invoke apex rest method" doc:id="800c854d-3e08-4fb5-9734-d40f2668eec6" config-ref="Salesforce_Config" className="MarketplaceIntegration" methodName="processTransaction^/marketplaceintegration/*^HttpPost^String^" target="vUpdateApexResponse" readTimeout="${salesforce.apex.readTimeout}" targetValue='#[output application/json --- {"processTransactionOutput": read(payload.processTransactionOutput, "application/json")}]'>
			<reconnect frequency="${salesforce.retry.timeperiod}" count="${salesforce.retry.max.retry}" />
		</salesforce:invoke-apex-rest-method>
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Response" doc:id="0b2d04bc-df7d-4c53-b9be-314563735ec6" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;  	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-mktplace-to-salesforce",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/purchaseData",&#10;	"BackendResponse": vars.vUpdateApexResponse,&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		<choice doc:name="Check Apex status" doc:id="43e69993-325f-4a04-8898-274834c7237e" >
			<when expression='#[!(vars.vUpdateApexResponse.processTransactionOutput.message ~= "FAILURE")]'>
				<logger level="INFO" doc:name="LOG INFO: Purchase Processed" doc:id="8e445c77-4e9b-48d7-90bd-9a98732bff92" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;  	"Message" : "Purchase Processed",&#10;	"FlowName" : "pf-mktplace-to-salesforce",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/purchaseData",&#10;	"BusinessKey": vars.vBusinessKey&#10;}]'/>
				<http:request method="PUT" doc:name="Update TRANSACTION_DETAILS" doc:id="ca1f3069-4267-4db3-afba-583cab4b7c14" config-ref="HTTPS_Request_DB_SYS_API" path="${https.request.dbSysApi.transactionDetails.path}" target="vTransactionResponse">
			<http:body><![CDATA[#[output application/json
---
{
  "transaction": {
    "STATUS": "COMPLETED",
    "LAST_UPDATED_BY": "PROCESS_API"
  }
}]]]></http:body>
			<http:query-params><![CDATA[#[output application/java
---
{
	"CORRELATION_ID": vars.vCorrelationId
}]]]></http:query-params>
		</http:request>
			</when>
			<otherwise >
				<logger level="ERROR" doc:name="LOG ERROR: Purchase not Processed" doc:id="f9db8eaa-84e1-47b4-9bcd-0324392f51e5" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;  	"Message" : "Purchase not Processed",&#10;	"FlowName" : "pf-mktplace-to-salesforce",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.vBusinessKey,&#10;	"Endpoint": "/api/purchaseData",&#10;	"ErrorDescription": write(vars.vUpdateApexResponse.processTransactionOutput.details default "")&#10;}]'/>
				<http:request method="PUT" doc:name="Update TRANSACTION_DETAILS" doc:id="f4ee7049-1cb9-4887-85ee-cd675164341c" config-ref="HTTPS_Request_DB_SYS_API" path="${https.request.dbSysApi.transactionDetails.path}" target="vTransactionResponse" >
					<http:body ><![CDATA[#[output application/json
---
{
  "transaction": {
    "STATUS": "FAILED",
    "ERROR_TYPE": "DATA",
    "ERROR_MSG": write(vars.vUpdateApexResponse.processTransactionOutput.message default "") ++ " | " ++ write(vars.vUpdateApexResponse.processTransactionOutput.details default ""),
    "LAST_UPDATED_BY": "PROCESS_API"
  }
}]]]></http:body>
					<http:query-params ><![CDATA[#[output application/java
---
{
	"CORRELATION_ID": vars.vCorrelationId
}]]]></http:query-params>
				</http:request>
			</otherwise>
		</choice>
		<set-variable value="#[%dw 2.0&#10;output application/json&#10;var statusFlag= !(vars.vUpdateApexResponse.processTransactionOutput.message ~= &quot;FAILURE&quot;)&#10;---&#10;{&#10;    &quot;fromAddress&quot;: p('smtp.user'),&#10;    &quot;toAddresses&quot;: p('smtp.systemFailure.toAddresses') splitBy &quot;,&quot;,&#10;    &quot;ccAddresses&quot;: [],&#10;    &quot;subject&quot;: (upper(p('mule.env')) ++ &quot; | &quot; ++ (if(statusFlag) &quot;NOTIFICATION&quot; else &quot;ERROR&quot;) ++  &quot; | &quot; ++ p('app.name') ++ &quot; | &quot; ++ (if(statusFlag) &quot;Purchase Processed Successfully&quot; else &quot;Purchase Not Processed&quot;) ++  &quot; | &quot; ++ (vars.vCorrelationId default &quot;&quot;)),&#10;    &quot;content&quot;: {&#10;        &quot;resource&quot;: p('app.name'),&#10;        &quot;priority&quot;: if(statusFlag) &quot;NOTIFICATION&quot; else &quot;ERROR&quot;,&#10;        &quot;recordId&quot;: payload.transactionDocumentation.transactionReceipt.stripeChargeId,&#10;        &quot;user&quot;: payload.transactionDocumentation.contactData.email,&#10;        &quot;recordName&quot;: payload.transactionDocumentation.accountData.companyName ++ '-'++ payload.transactionDocumentation.transactionReceipt.stripeChargeId,&#10;        &quot;code&quot;: &quot;200&quot;,&#10;        &quot;messageType&quot;: vars.errorDescription.message,&#10;        &quot;description&quot;: {&#10;            &quot;sourceSystem&quot;: &quot;MARKETPLACE&quot;,&#10;            &quot;destinationSystem&quot;: &quot;SALESFORCE&quot;,&#10;            &quot;operation&quot;: &quot;CREATE&quot;,&#10;            &quot;object&quot;: &quot;DEAL&quot;,&#10;            &quot;transactionId&quot;: vars.vTransactionId,&#10;            &quot;message&quot;: if(statusFlag) (vars.vUpdateApexResponse.processTransactionOutput.message default &quot;&quot;) else (vars.vUpdateApexResponse.processTransactionOutput.message default &quot;Purchase Not Processed&quot;),&#10;            &quot;description&quot;: (&#10;                if(statusFlag) &quot;Opportunity &quot; ++ (vars.vUpdateApexResponse.processTransactionOutput.details.Opportunity.Name default &quot;&quot;) ++ &quot; has been saved in Salesforce&quot; else write(vars.vUpdateApexResponse.processTransactionOutput.details default &quot;&quot;)&#10;            )&#10;        },&#10;        &quot;correlationId&quot;: vars.vCorrelationId default &quot;&quot;,&#10;        &quot;timestamp&quot;: now() &gt;&gt; 'IST'&#10;    },&#10;    &quot;slackNotificationFlag&quot;: &quot;1&quot;&#10;}]" doc:name="vEmailDetails" doc:id="8b4d322d-a561-4f1d-a007-a7ec43559834" variableName="vEmailDetails" />
		<flow-ref doc:name="Flow Reference to sf-send-notification" doc:id="54f4fa2f-7765-49c2-805e-0c9631988abb" name="sf-send-notification" />
		<ee:transform doc:name="Set payload, httpStatus" doc:id="8d835a54-ac7d-40dc-8eda-ae596ddbb647">
			<ee:message>
				<ee:set-payload><![CDATA[%dw 2.0
output application/json
---
vars.vUpdateApexResponse.processTransactionOutput]]></ee:set-payload>
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="httpStatus" ><![CDATA[200]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<logger level="DEBUG" doc:name="LOG DEBUG: Outbound Response Payload" doc:id="0f8400c2-714d-46a0-a3db-bd04f669b545" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Log Outbound Response Payload", &#10;	"FlowName" : "pf-mktplace-to-salesforce", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/purchaseData",&#10;	"SFResponse": payload,&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Outbound Response" doc:id="09361eb8-f95b-4f46-9b3b-60aa5b9bbbeb" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Response",&#10;	"FlowName" : "pf-mktplace-to-salesforce",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/purchaseData",&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit" doc:id="70fed101-4fcf-4040-952d-66d87f1642f0" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Market place purchaseData process flow ended", &#10;	"FlowName" : "pf-mktplace-to-salesforce", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		
	</flow>
</mule>