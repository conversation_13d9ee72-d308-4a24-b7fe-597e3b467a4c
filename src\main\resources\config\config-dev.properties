### API ###
api.autodiscoveryId=19986644

### HTTPS Listener ###
https.listener.keystore.path=certificates/keystore-dev.jks
https.listener.truststore.path=certificates/truststore-dev.jks

### HTTPS Request Mule API###
https.request.muleApi.truststore.path=certificates/truststore-dev.jks

### Retry stategy ###
salesforce.retry.max.retry=2
salesforce.retry.timeperiod=1500

### Salesforce ###
salesforce.keystorePath=certificates/sf-keystore-dev.jks
salesforce.principal=<EMAIL>
salesforce.tokenEndpoint=https://3degrees--devsandbox.sandbox.my.salesforce.com/services/oauth2/token
salesforce.audienceUrl=https://test.salesforce.com
salesforce.externalCallbackUrl=https://mktplace-sf-api-dev-kby5ju.1avcn3.usa-w2.cloudhub.io/callback
salesforce.authorizationUrl=https://3degrees--devsandbox.sandbox.my.salesforce.com/services/oauth2/authorize
salesforce.callbackPath=/callback
salesforce.authorizePath=/authorize
salesforce.apex.readTimeout=60000

### DB SYS API ###
https.request.dbSysApi.host=transactiondb-sys-api-dev-kby5ju.1avcn3.usa-w2.cloudhub.io
https.request.dbSysApi.port=443
https.request.dbSysApi.basePath=/
https.request.dbSysApi.idleTimeout=10000
https.request.dbSysApi.responseTimeout=60000
https.request.dbSysApi.maxConnections=30
https.request.dbSysApi.reconnection.frequency=1500
https.request.dbSysApi.reconnection.attempts=3
https.request.dbSysApi.truststore.path=certificates/truststore-dev.jks

### STMP ###
smtp.host=smtp.gmail.com
smtp.port=587
smtp.user=<EMAIL>
smtp.dataFailure.toAddresses=<EMAIL>
smtp.systemFailure.toAddresses=<EMAIL>
smtp.connectionTimeout=5
smtp.readTimeout=5
smtp.writeTimeout=5
smtp.reconnection.frequency=1500
smtp.reconnection.attempts=3

### Template ###
crudErrorTemplate.path=templates/crudFailure.html

### Slack Object store ###
# DAYS #
slackOs.entryTtl=1
slackOs.expirationInterval=-1

### Slack ###
slack.baseUri=https://slack.com/api
slack.connectionTimeout=30000
slack.maxConnections=-1
slack.connectionIdleTimeout=30000
slack.authorizationUrl=https://slack.com/oauth/v2/authorize
slack.accessTokenUrl=https://slack.com/api/oauth.v2.access
slack.resourceOwnerId=mule-user
slack.accessScopes=channels:read chat:write
slack.callbackPath=/callback
slack.authorizePath=/authorize
slack.externalCallbackUrl=https://mktplace-sf-api-dev-kby5ju.1avcn3.usa-w2.cloudhub.io/callback
slack.notitificationChannel=3d-mulesoft-order2cash-dev
slack.notificationFlag=1


### HTTPS Request syncPrc API ###
https.request.syncPrcApi.host=sync-prc-api-dev-kby5ju.1avcn3.usa-w2.cloudhub.io
https.request.syncPrcApi.port=443
https.request.syncPrcApi.basePath=/
https.request.syncPrcApi.connectionTimeout=30000
https.request.syncPrcApi.responseTimeout=30000
https.request.syncPrcApi.reconnection.frequency=1000
https.request.syncPrcApi.reconnection.attempts=3

### Artemis SYS API ###
https.request.artemisSysApi.host=artemis-sys-api-dev-kby5ju.internal-1avcn3.usa-w2.cloudhub.io
https.request.artemisSysApi.port=443
https.request.artemisSysApi.basePath=/
https.request.artemisSysApi.idleTimeout=30000
https.request.artemisSysApi.responseTimeout=60000
https.request.artemisSysApi.maxConnections=-1
https.request.artemisSysApi.reconnection.frequency=1500
https.request.artemisSysApi.reconnection.attempts=3
https.request.artemisSysApi.truststore.path=certificates/truststore-dev.jks


### Enable Sync for Artemis ###
enableSyncForCP=1