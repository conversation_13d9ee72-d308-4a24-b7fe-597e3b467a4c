<?xml version="1.0" encoding="UTF-8"?>
<mule xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:apikit="http://www.mulesoft.org/schema/mule/mule-apikit" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core" xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd http://www.mulesoft.org/schema/mule/mule-apikit http://www.mulesoft.org/schema/mule/mule-apikit/current/mule-apikit.xsd http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd ">

    <flow name="3degreesMktplaceSFAPI-main">
        <http:listener config-ref="HTTPS_Listener_config" path="/api/*" doc:name="/api/*">
            <http:response statusCode="#[vars.httpStatus default 200]">
                <http:headers><![CDATA[#[vars.outboundHeaders default {}]]]></http:headers>
            </http:response>
            <http:error-response statusCode="#[vars.httpStatus default 500]">
                <http:body><![CDATA[#[payload]]]></http:body>
                <http:headers><![CDATA[#[vars.outboundHeaders default {}]]]></http:headers>
            </http:error-response>
        </http:listener>
        <ee:transform doc:name="Set vCorrelationId, vTransactionId, vAttributes, vBusinessKey" doc:id="a663488f-8fc9-41b5-ad28-a4385d97cca8">
            <ee:message />
            <ee:variables>
                <ee:set-variable variableName="vCorrelationId"><![CDATA[attributes.headers.'correlationId' default ""]]></ee:set-variable>
                <ee:set-variable variableName="vTransactionId"><![CDATA[attributes.headers.'x-transactionId' default ""]]></ee:set-variable>
                <ee:set-variable variableName="vAttributes"><![CDATA[%dw 2.0
output application/json
---
{
	"headers": attributes.headers,
	"queryParams": attributes.queryParams,
	"uriParams": attributes.uriParams
}]]></ee:set-variable>
                <ee:set-variable variableName="vBusinessKey"><![CDATA["AccountID- " ++ (payload.transactionDocumentation.accountData.accountId default "") ++ 
", ContactID- " ++ (payload.transactionDocumentation.contactData.contactId default "") ++  
", StripeInvoiceId- " ++ (payload.transactionDocumentation.purchaseData.stripeInvoiceId default "")]]></ee:set-variable>
            </ee:variables>
        </ee:transform>
        <apikit:router config-ref="3degreesMktplaceSFAPI-config" />
        <error-handler ref="global-error-handler" />
    </flow>
    <flow name="put:\purchaseData:application\json:3degreesMktplaceSFAPI-config">
        <flow-ref doc:name="Flow Reference to pf-mktplace-to-salesforce" doc:id="7fc9a147-d4e1-4251-adcc-2159b3f6ba13" name="pf-mktplace-to-salesforce" />
    </flow>
<!-- [STUDIO:"put:\company:application\json:3degreesMktplaceSFAPI-config"]    <flow name="put:\company:application\json:3degreesMktplaceSFAPI-config">
        <ee:transform doc:name="Transform Message">
            <ee:message>
                <ee:set-payload><![CDATA[%dw 2.0
output application/json
&#45;&#45;-
{
  code: 201,
  transactionId: "123123",
  status: "SUCCESS",
  response: {
    accountId: 122344,
    eid: "EID_ACC_1234"
  }
} as Object {encoding: "UTF-8", mediaType: "application/json"}&#93;&#93;></ee:set-payload>
            </ee:message>
        </ee:transform>
    </flow> [STUDIO] -->
<!-- [STUDIO:"post:\company:application\json:3degreesMktplaceSFAPI-config"]    <flow name="post:\company:application\json:3degreesMktplaceSFAPI-config">
         <flow-ref doc:name="Flow Reference to pf-on-dp-account-created" doc:id="b5819155-dd9f-4ce2-a127-ae5f9d4768f3" name="pf-on-dp-account-created" />
    </flow> [STUDIO] -->
    <flow name="post:\CPCounterparty:application\json:3degreesMktplaceSFAPI-config">
         <flow-ref doc:name="Flow Reference to pf-on-dp-counterParty-created" doc:id="fc310c3f-7f9f-40e1-8a4b-2c4c7cf72fc6" name="pf-on-dp-counterParty-created" />
    </flow>
	 <flow name="post:\supplier-reach\rfq:application\json:3degreesMktplaceSFAPI-config">
        <flow-ref doc:name="Flow Reference to pf-supplierREach-to-salesforce" doc:id="8877ca33-286b-4d8f-a06b-8a0e4eac179a" name="pf-supplierREach-to-salesforce" />
    </flow>
</mule>
