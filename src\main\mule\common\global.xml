<?xml version="1.0" encoding="UTF-8"?>

<mule
xmlns:salesforce="http://www.mulesoft.org/schema/mule/salesforce"
	xmlns:oauth="http://www.mulesoft.org/schema/mule/oauth"
	
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:slack="http://www.mulesoft.org/schema/mule/slack"
	xmlns:api-gateway="http://www.mulesoft.org/schema/mule/api-gateway"
	xmlns:apikit="http://www.mulesoft.org/schema/mule/mule-apikit"
	xmlns:email="http://www.mulesoft.org/schema/mule/email"
	xmlns:validation="http://www.mulesoft.org/schema/mule/validation"
	xmlns:os="http://www.mulesoft.org/schema/mule/os"
	xmlns:secure-properties="http://www.mulesoft.org/schema/mule/secure-properties"
	xmlns:tls="http://www.mulesoft.org/schema/mule/tls" xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns:vm="http://www.mulesoft.org/schema/mule/vm" xmlns:db="http://www.mulesoft.org/schema/mule/db" xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd 
http://www.mulesoft.org/schema/mule/mule-apikit http://www.mulesoft.org/schema/mule/mule-apikit/current/mule-apikit.xsd 
http://www.mulesoft.org/schema/mule/tls http://www.mulesoft.org/schema/mule/tls/current/mule-tls.xsd 
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd
http://www.mulesoft.org/schema/mule/vm http://www.mulesoft.org/schema/mule/vm/current/mule-vm.xsd
http://www.mulesoft.org/schema/mule/secure-properties http://www.mulesoft.org/schema/mule/secure-properties/current/mule-secure-properties.xsd
http://www.mulesoft.org/schema/mule/os http://www.mulesoft.org/schema/mule/os/current/mule-os.xsd
http://www.mulesoft.org/schema/mule/validation http://www.mulesoft.org/schema/mule/validation/current/mule-validation.xsd
http://www.mulesoft.org/schema/mule/email http://www.mulesoft.org/schema/mule/email/current/mule-email.xsd
http://www.mulesoft.org/schema/mule/api-gateway http://www.mulesoft.org/schema/mule/api-gateway/current/mule-api-gateway.xsd
http://www.mulesoft.org/schema/mule/slack http://www.mulesoft.org/schema/mule/slack/current/mule-slack.xsd
http://www.mulesoft.org/schema/mule/oauth http://www.mulesoft.org/schema/mule/oauth/current/mule-oauth.xsd
http://www.mulesoft.org/schema/mule/salesforce http://www.mulesoft.org/schema/mule/salesforce/current/mule-salesforce.xsd">
      
      <api-gateway:autodiscovery apiId="${api.autodiscoveryId}" ignoreBasePath="true" doc:name="API Autodiscovery" doc:id="7e9cc5cf-6ccf-48c9-bc9e-39c68ae50fe7" flowRef="3degreesMktplaceSFAPI-main" />
	
      
       <apikit:config name="3degreesMktplaceSFAPI-config" api="resource::a970b687-ceb1-48a0-9bc7-6fed0e331363:mktplace_sf_api:1.0.15:raml:zip:3degreesMktplaceSFAPI.raml" outboundHeadersMapName="outboundHeaders" httpStatusVarName="httpStatus" />
    <http:listener-config name="HTTPS_Listener_config" doc:name="HTTP Listener config" doc:id="6b32dc59-1a1d-4d5b-8aa4-25a6be4d6bac">
        <http:listener-connection host="${https.listener.host}" port="${https.listener.port}" readTimeout="${https.listener.readTimeout}" connectionIdleTimeout="${https.listener.idleTimeout}" protocol="HTTPS" tlsContext="TLS_Context_Inbound" />
    </http:listener-config>
    
	 <tls:context name="TLS_Context_Inbound" doc:name="TLS Context" doc:id="34cdff78-e206-4ea9-9357-151a2baeedfc" >
		<tls:key-store type="jks" path="${https.listener.keystore.path}" keyPassword="${secure::https.listener.keystore.keyPassword}" password="${secure::https.listener.keystore.password}" />
	</tls:context>
	<tls:context name="TLS_Context_Outbound_Transaction_DB" doc:name="TLS Context" doc:id="ffcf6b1f-673b-42a3-b830-c4778e954f47" >
		<tls:trust-store path="${https.request.dbSysApi.truststore.path}" password="${secure::https.request.dbSysApi.truststore.password}" type="jks"/>
	</tls:context>
	
	<configuration-properties
		doc:name="Configuration properties"
		doc:id="4b453e82-c28b-4813-890f-ee5ab843e6b7"
		file="config\config-common.properties" />
	<configuration-properties
		doc:name="Configuration properties"
		doc:id="f998f385-a708-43df-8601-853f603e14bc"
		file="config\config-${mule.env}.properties" />
	<secure-properties:config
		name="Secure_Properties_Config" doc:name="Secure Properties Config"
		doc:id="b31234aa-f90a-44dd-a3c4-8179cf1a1fea"
		file="config\config-${mule.env}-secure.properties" key="${mule.key}">
		<secure-properties:encrypt
			algorithm="Blowfish" />
	</secure-properties:config>
	<http:request-config name="HTTPS_Request_DB_SYS_API"
		doc:name="HTTP Request configuration"
		doc:id="b6d7504b-8d3d-4278-965c-6768bdbad8be"
		responseTimeout="#[p('https.request.dbSysApi.responseTimeout')]"
		basePath="#[p('https.request.dbSysApi.basePath')]">
		<http:request-connection protocol="HTTPS"
			host="#[p('https.request.dbSysApi.host')]"
			port="#[p('https.request.dbSysApi.port')]"
			connectionIdleTimeout="${https.request.dbSysApi.idleTimeout}" maxConnections="${https.request.dbSysApi.maxConnections}">
			<reconnection >
				<reconnect frequency="${https.request.dbSysApi.reconnection.frequency}" count="${https.request.dbSysApi.reconnection.attempts}" />
			</reconnection>
			<tls:context >
				<tls:trust-store insecure="true" />
			</tls:context>
		</http:request-connection>
		<http:default-headers>
			<http:default-header key="client_id"
				value="#[p('secure::https.request.dbSysApi.headers.clientId')]" />
			<http:default-header key="client_secret"
				value="#[p('secure::https.request.dbSysApi.headers.clientSecret')]" />
			<http:default-header key="correlationId" value="#[vars.vCorrelationId]" />
			<http:default-header key="x-businessKey" value='#[vars.vBusinessKey default ""]' />
		</http:default-headers>
	</http:request-config>
	<os:object-store name="Object_store_Slack" doc:name="Object store" doc:id="51652b38-b59e-4eef-80cf-de44ac6db27f" entryTtl="${slackOs.entryTtl}" expirationInterval="${slackOs.expirationInterval}" entryTtlUnit="DAYS" expirationIntervalUnit="DAYS"/>
	<email:smtp-config name="Email_SMTP" doc:name="Email SMTP" doc:id="3cd9f4a4-f69b-4472-88fa-d04f772d0c58" >
		<email:smtp-connection host="#[p('smtp.host')]" port="#[p('smtp.port')]" user="#[p('smtp.user')]" password="${secure::smtp.password}" connectionTimeout="#[p('smtp.connectionTimeout')]" readTimeout="#[p('smtp.readTimeout')]" writeTimeout="#[p('smtp.writeTimeout')]">
			<reconnection >
				<reconnect frequency="${smtp.reconnection.frequency}" count="${smtp.reconnection.attempts}" />
			</reconnection>
			<email:properties >
				<email:property key="mail.smtp.starttls.enable" value="true" />
			</email:properties>
		</email:smtp-connection>
	</email:smtp-config>
	<slack:config name="Slack_Connector_Config" doc:name="Slack Connector Config" doc:id="bea078fc-639f-4629-8ac6-64202422999d" >
		<slack:slack-auth-connection baseUri="#[p('slack.baseUri')]" connectionTimeout="${slack.connectionTimeout}" maxConnections="${slack.maxConnections}" connectionIdleTimeout="${slack.connectionIdleTimeout}" connectionTimeoutUnit="MILLISECONDS" connectionIdleTimeoutUnit="MILLISECONDS">
			<slack:oauth-authorization-code consumerKey="${secure::slack.consumerKey}" consumerSecret="${secure::slack.consumerSecret}" authorizationUrl="${slack.authorizationUrl}" accessTokenUrl="${slack.accessTokenUrl}" scopes="${slack.accessScopes}"/>
			<slack:oauth-callback-config listenerConfig="HTTPS_Listener_config" callbackPath="${slack.callbackPath}" authorizePath="${slack.authorizePath}" externalCallbackUrl="${slack.externalCallbackUrl}" />
			<slack:oauth-store-config objectStore="Object_store_Slack" />
		</slack:slack-auth-connection>
	</slack:config>
	
	
	<!-- <configuration doc:name="Configuration" doc:id="6c9329f3-9319-458d-b0e1-39cb373e976b" defaultErrorHandler-ref="global-error-handler" /> -->
    <salesforce:sfdc-config name="Salesforce_Config"
		doc:name="Salesforce Config"
		doc:id="0bb3169f-7878-445d-b7db-4bed095076d5">
		<salesforce:jwt-connection
			consumerKey="${secure::salesforce.consumerKey}"
			keyStore="${salesforce.keystorePath}"
			storePassword="${secure::salesforce.keystorePassword}"
			certificateAlias="${secure::salesforce.certificateAlias}"
			principal="${salesforce.principal}"
			tokenEndpoint="${salesforce.tokenEndpoint}"
			audienceUrl="${salesforce.audienceUrl}" />
	</salesforce:sfdc-config>
	
	<!-- <http:request-config
		name="HTTPS_Request_Sync_Prc_API"
		doc:name="HTTP Request configuration"
		doc:id="4b2ab22e-1682-4977-878d-1b91a91259ab" basePath="#[p('https.request.syncPrcApi.basePath')]">
		<http:request-connection
			host="${https.request.syncPrcApi.host}" protocol="HTTPS"
			port="${https.request.syncPrcApi.port}"
			connectionIdleTimeout="${https.request.syncPrcApi.connectionTimeout}" tlsContext="TLS_Context_Outbound_Transaction_DB">
			<reconnection>
				<reconnect
					frequency="${https.request.syncPrcApi.reconnection.frequency}"
					count="${https.request.syncPrcApi.reconnection.attempts}" />
			</reconnection>
		</http:request-connection>
		<http:default-headers>
			<http:default-header key="client_id"
				value="#[p('secure::https.request.syncPrcApi.headers.clientId')]" />
			<http:default-header key="client_secret"
				value="#[p('secure::https.request.syncPrcApi.headers.clientSecret')]" />
		</http:default-headers>
	</http:request-config> -->

	<!-- <tls:context name="TLS_Context_Mule_Api_Outbound" doc:name="TLS Context" doc:id="436f0be5-9048-4dd0-8227-8e0943db9aae" >
		<tls:trust-store path="${https.request.muleApi.truststore.path}" password="${secure::https.request.muleApi.truststore.password}" type="jks"/>
	</tls:context> -->
	
	
	<!-- <http:request-config name="HTTPS_Request_Transaction_DB_SYS_API" doc:name="HTTP Request configuration" doc:id="d00486f1-0049-468d-88dc-22adb708a334" responseTimeout="#[p('https.request.transactionDBSysApi.responseTimeout')]">
		<http:request-connection protocol="HTTPS" host="#[p('https.request.transactionDBSysApi.host')]" port="#[p('https.request.transactionDBSysApi.port')]" connectionIdleTimeout="${https.request.transactionDBSysApi.connectionTimeout}" tlsContext="TLS_Context_Mule_Api_Outbound">
			<reconnection >
				<reconnect frequency="${https.request.transactionDBSysApi.reconnection.frequency}" count="${https.request.transactionDBSysApi.reconnection.attempts}" />
			</reconnection>
		</http:request-connection>
		<http:default-headers >
			<http:default-header key="client_id" value="#[p('secure::https.request.transactionDBSysApi.headers.clientId')]" />
			<http:default-header key="client_secret" value="#[p('secure::https.request.transactionDBSysApi.headers.clientSecret')]" />
		</http:default-headers>
	</http:request-config> -->
	
	<http:request-config
		name="HTTPS_Request_Artemis_SYS_API"
		doc:name="HTTP Request configuration"
		doc:id="9c724d16-e400-4b4c-ba06-5d1be3307d73"
		responseTimeout="#[p('https.request.artemisSysApi.responseTimeout')]"
		basePath="#[p('https.request.artemisSysApi.basePath')]">
		<http:request-connection protocol="HTTPS"
			host="#[p('https.request.artemisSysApi.host')]"
			port="#[p('https.request.artemisSysApi.port')]"
			connectionIdleTimeout="${https.request.artemisSysApi.idleTimeout}" maxConnections="${https.request.artemisSysApi.maxConnections}">
			<reconnection >
				<reconnect frequency="${https.request.artemisSysApi.reconnection.frequency}" count="${https.request.artemisSysApi.reconnection.attempts}" />
			</reconnection>
			<tls:context >
				<tls:trust-store insecure="true" />
			</tls:context>
		</http:request-connection>
		<http:default-headers>
			<http:default-header key="client_id"
				value="#[p('secure::https.request.artemisSysApi.headers.clientId')]" />
			<http:default-header key="client_secret"
				value="#[p('secure::https.request.artemisSysApi.headers.clientSecret')]" />
			<http:default-header key="correlationId" value="#[vars.vCorrelationId]" />
		</http:default-headers>
	</http:request-config>
	
	</mule>