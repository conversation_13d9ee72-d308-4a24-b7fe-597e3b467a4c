<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:slack="http://www.mulesoft.org/schema/mule/slack"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core" xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns:email="http://www.mulesoft.org/schema/mule/email"
	xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/email http://www.mulesoft.org/schema/mule/email/current/mule-email.xsd http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/slack http://www.mulesoft.org/schema/mule/slack/current/mule-slack.xsd">
	
	<sub-flow name="sf-send-notification" doc:id="584bbf82-f19f-41b5-a473-a3d58800f919" >

		<ee:transform doc:name="Filter toAddresses, ccAddresses" doc:id="d56cacc5-9f3f-487b-89a9-052ea061cc5b" >
			<ee:variables >
				<ee:set-variable variableName="vEmailDetails" ><![CDATA[%dw 2.0
var validEmails = (
	(p('smtp.dataFailure.toAddresses') splitBy ",") ++
	(p('smtp.systemFailure.toAddresses') splitBy ",")
) distinctBy ($)
output application/json
---
vars.vEmailDetails  update {
    case toAddresses at .toAddresses -> (toAddresses filter(validEmails contains ($)) default []) distinctBy ($)
    case ccAddresses at .ccAddresses -> (ccAddresses filter(validEmails contains ($)) default []) distinctBy ($)
}]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		
		<logger level="INFO" doc:name="LOG INFO: Sending email" doc:id="9fedfa0c-aea1-4b66-86d1-fdaed49303a4" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Sending email alert...",&#10;  	"Content": (vars.vEmailDetails), &#10;	"FlowName" : "sf-send-notification", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]'/>
		
		<!--<email:send doc:id="b3294733-0107-4093-b09c-1778d5dc2b7a" config-ref="Email_SMTP" toAddresses="#[(vars.vEmailDetails.'toAddresses' default []) - &quot;&quot; - null]" doc:name="Send email alert" fromAddress="#[vars.vEmailDetails.'fromAddress']" ccAddresses="#[(vars.vEmailDetails.'ccAddresses' default []) - &quot;&quot; - null]" subject="#[vars.vEmailDetails.'subject']">
			<email:body>
				<email:content><![CDATA[#[output text/plain
-&#45;&#45;
write(vars.vEmailDetails.'content', "application/json")]]]></email:content>
			</email:body>
		</email:send> -->
		
		<parse-template doc:name="Parse Template" doc:id="42e71f85-373b-41c0-8863-73e7508877ff" location="${crudErrorTemplate.path}" outputMimeType="text/html" target="vParsedTemplate"/>
		<email:send doc:name="Send email alert" doc:id="b8d9b65e-e196-4666-a63d-ebc4a776b6e4" config-ref="Email_SMTP" fromAddress="#[vars.vEmailDetails.'fromAddress']" subject="#[vars.vEmailDetails.'subject']" toAddresses="#[(vars.vEmailDetails.'toAddresses' default []) - &quot;&quot; - null]" ccAddresses="#[(vars.vEmailDetails.'ccAddresses' default []) - &quot;&quot; - null]" >
			<email:body contentType="text/html">
				<email:content ><![CDATA[#[vars.vParsedTemplate]]]></email:content>
			</email:body>
		</email:send>
		
		<choice doc:name="Is slack notification required" doc:id="978152a0-7fd2-4183-8e89-258dd86067a2" >
			<when expression="#[((!isEmpty(vars.vEmailDetails.'slackNotificationFlag')) and (vars.vEmailDetails.'slackNotificationFlag' ~= &quot;1&quot;))]">
				<logger level="INFO" doc:name="LOG INFO: Sending slack notification" doc:id="795e22db-569c-4745-aa46-b63b21513fd7" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Sending slack notification...",&#10;	"FlowName" : "sf-send-notification", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]'/>
				<async doc:name="Async" doc:id="fc102ec5-1797-44ab-9c8f-4c3dab95f174" >
					<set-variable value="#[output application/json&#10;---&#10;{&#10;	&quot;channel&quot;: p('slack.notitificationChannel'),&#10;	&quot;text&quot;: (&#10;		&quot;\n========================================================================================\n&quot; ++&#10;		(write(vars.vEmailDetails.'content', &quot;application/json&quot;)) as String ++&#10;		&quot;\n========================================================================================\n&quot;&#10;	)&#10;}]" doc:name="vSlackMessageContent" doc:id="bddbd4c1-73d6-44db-bc63-a6f5aef956be" variableName="vSlackMessageContent" />
					<slack:create-chatpost-message doc:name="Send Notification" doc:id="e05f5239-f1d6-4348-9de5-68e805e413ce" config-ref="Slack_Connector_Config" target="vSlackNotificationOutput" >
						<slack:chatpost-message-content ><![CDATA[#[vars.vSlackMessageContent]]]></slack:chatpost-message-content>
					</slack:create-chatpost-message>
					<remove-variable doc:name="vSlackMessageContent" doc:id="f0df60bf-4289-45c5-bbbb-193012feb8c1" variableName="vSlackMessageContent" />
				</async>
			</when>
		</choice>
	</sub-flow>
	
	<!-- <sub-flow name="sf-send-crud-failure-alert-email" doc:id="584bbf82-f19f-41b5-a473-a3d58800f919" >
		<parse-template doc:name="Parse Template" doc:id="7e73b76f-9ce0-4c1a-9623-87f444851466" location="templates\crudFailure.html" outputMimeType="text/html" target="vParsedTemplate"/>
		<email:send doc:name="Send email alert" doc:id="60443b4c-0a3d-4135-9fba-e677a386af20" config-ref="Email_SMTP" fromAddress="#[vars.vEmailDetails.'fromAddress']" subject="#[vars.vEmailDetails.'subject']" toAddresses="#[(vars.vEmailDetails.'toAddresses' default []) - &quot;&quot; - null]" ccAddresses="#[(vars.vEmailDetails.'ccAddresses' default []) - &quot;&quot; - null]" >
			<email:body contentType="text/html">
				<email:content ><![CDATA[#[vars.vParsedTemplate]]]></email:content>
			</email:body>
		</email:send>
	</sub-flow> -->
	
	<!-- <sub-flow name="sf-send-generic-alert-email" doc:id="63cc825d-9d63-4158-967e-a72ff4e84144" >
		<parse-template doc:name="Parse Template" doc:id="e86f3c1e-1e57-4bab-9b44-064c46ed750c" location="templates\genericAlert.html" outputMimeType="text/html" />
		<email:send doc:name="Send email alert" doc:id="8f7aa914-92bb-4db5-b0af-4c76c334206c" config-ref="Email_SMTP" fromAddress="#[vars.vEmailDetails.'fromAddress']" subject="#[vars.vEmailDetails.'subject']" toAddresses="#[vars.vEmailDetails.'toAddresses']" ccAddresses="#[vars.vEmailDetails.'ccAddresses']" >
			<email:body contentType="text/html" >
			</email:body>
		</email:send>
	</sub-flow> -->
	
</mule>