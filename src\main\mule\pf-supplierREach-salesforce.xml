<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core" xmlns:salesforce="http://www.mulesoft.org/schema/mule/salesforce"
	xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/salesforce http://www.mulesoft.org/schema/mule/salesforce/current/mule-salesforce.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd">
<flow name="pf-supplierREach-to-salesforce" doc:id="ef414e25-687b-458a-b081-2f0e9c268081" >
		<logger level="INFO" doc:name="LOG INFO: Log Entry" doc:id="ebe3bde7-e2f0-4e46-b8b1-5b829b1e426a" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "SupplierREach process flow started", &#10;	"FlowName" : "pf-supplierREach-to-salesforce", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.vBusinessKey	&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Inbound Request" doc:id="fe2607d1-2ea8-4bb7-8c9a-43bd656fb3a5" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Inbound Request",&#10;	"FlowName" : "pf-supplierREach-to-salesforce",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/supplier-reach/rfq",&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		<logger level="DEBUG" doc:name="LOG DEBUG: Inbound Request Payload" doc:id="9b2fe72f-f613-4cc5-aa54-0a2d0c7d48ba" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Log Inbound Request Payload", &#10;	"FlowName" : "pf-supplierREach-to-salesforce",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/supplier-reach/rfq",&#10;	"BackendRequest": payload,&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		<http:request method="POST" doc:name="Create entry in TRANSACTION_DETAILS" doc:id="2c8d703a-7c91-47f1-9df2-903221755aa9" config-ref="HTTPS_Request_DB_SYS_API" path="${https.request.dbSysApi.transactionDetails.path}" target="vTransactionResponse">
			<http:body><![CDATA[#[%dw 2.0
output application/json
---
{
  "transaction": {
    "CORRELATION_ID": vars.vCorrelationId,
    "OPERATION": "CREATE",
    "SOURCE": "SUPPLIER_REACH",
    "STATUS": "STARTED",
    "LAST_UPDATED_BY": "PROCESS_API",
    "ENTERPRISE_ID":  "",
    "PAYLOAD": write(payload,'application/json'),
    "RECORD_ID": payload.transactionDocumentation.recordId as String,
    "OBJECT_TYPE": "SUPPLIER_REACH_RFQ",
    "PRIORITY": ("0") as Number,
    "RETRY_COUNT": 0,
    "QUERY_PARAMS": null,
    "ERROR_MSG": "",
    "ERROR_TYPE": "",
    "USER": null,
    "RECORD_NAME": payload.transactionDocumentation.accountData.companyName ++ "-" ++ payload.transactionDocumentation.contactData.lastName
  }
}]]]></http:body>
		</http:request>
		<salesforce:invoke-apex-rest-method doc:name="Invoke apex rest method" doc:id="fe8eaed0-bbb7-46e6-b23c-c44391a7af6e" config-ref="Salesforce_Config" className="SupplierREachIntegration" methodName="processTransaction^/supplierreachintegration/*^HttpPost^String^" target="vUpdateApexResponse" readTimeout="${salesforce.apex.readTimeout}" targetValue='#[output application/json --- {"processTransactionOutput": read(payload.processTransactionOutput, "application/json")}]'>
			<reconnect frequency="${salesforce.retry.timeperiod}" count="${salesforce.retry.max.retry}" />
		</salesforce:invoke-apex-rest-method>
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Response" doc:id="529aeaef-8046-4b4b-a3fb-52cb1606803c" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;  	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-supplierREach-to-salesforce",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/supplier-reach/rfq",&#10;	"BackendResponse": vars.vUpdateApexResponse,&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		<choice doc:name="Check Apex status" doc:id="afe20e5a-a025-4391-baf9-876574eb8e6c" >
			<when expression='#[!(vars.vUpdateApexResponse.processTransactionOutput.message ~= "FAILURE")]'>
				<logger level="INFO" doc:name="LOG INFO: SupplierREach Processed" doc:id="d5370095-cfd6-4546-9339-3a2ec3103270" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;  	"Message" : "SupplierREach Processed",&#10;	"FlowName" : "pf-supplierREach-to-salesforce",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/supplier-reach/rfq",&#10;	"BusinessKey": vars.vBusinessKey&#10;}]'/>
				<http:request method="PUT" doc:name="Update TRANSACTION_DETAILS" doc:id="411be884-446b-4902-8b44-0e90726efe8e" config-ref="HTTPS_Request_DB_SYS_API" path="${https.request.dbSysApi.transactionDetails.path}" target="vTransactionResponse">
			<http:body><![CDATA[#[output application/json
---
{
  "transaction": {
    "STATUS": "COMPLETED",
    "LAST_UPDATED_BY": "PROCESS_API"
  }
}]]]></http:body>
			<http:query-params><![CDATA[#[output application/java
---
{
	"CORRELATION_ID": vars.vCorrelationId
}]]]></http:query-params>
		</http:request>
			</when>
			<otherwise >
				<logger level="ERROR" doc:name="LOG ERROR: SupplierREach not Processed" doc:id="8f674685-6f8a-45cb-bdb0-c840e21551f6" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;  	"Message" : "SupplierREach not Processed",&#10;	"FlowName" : "pf-supplierREach-to-salesforce",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.vBusinessKey,&#10;	"Endpoint": "/api/supplier-reach/rfq",&#10;	"ErrorDescription": write(vars.vUpdateApexResponse.processTransactionOutput.details default "")&#10;}]'/>
				<http:request method="PUT" doc:name="Update TRANSACTION_DETAILS" doc:id="a545f7e7-a4f6-4aeb-9fe2-f2458c4a8802" config-ref="HTTPS_Request_DB_SYS_API" path="${https.request.dbSysApi.transactionDetails.path}" target="vTransactionResponse" >
					<http:body ><![CDATA[#[output application/json
---
{
  "transaction": {
    "STATUS": "FAILED",
    "ERROR_TYPE": "DATA",
    "ERROR_MSG": write(vars.vUpdateApexResponse.processTransactionOutput.message default "") ++ " | " ++ write(vars.vUpdateApexResponse.processTransactionOutput.details default ""),
    "LAST_UPDATED_BY": "PROCESS_API"
  }
}]]]></http:body>
					<http:query-params ><![CDATA[#[output application/java
---
{
	"CORRELATION_ID": vars.vCorrelationId
}]]]></http:query-params>
				</http:request>
			</otherwise>
		</choice>
		<set-variable value="#[%dw 2.0&#10;output application/json&#10;var statusFlag= !(vars.vUpdateApexResponse.processTransactionOutput.message ~= &quot;FAILURE&quot;)&#10;---&#10;{&#10;    &quot;fromAddress&quot;: p('smtp.user'),&#10;    &quot;toAddresses&quot;: p('smtp.systemFailure.toAddresses') splitBy &quot;,&quot;,&#10;    &quot;ccAddresses&quot;: [],&#10;    &quot;subject&quot;: (upper(p('mule.env')) ++ &quot; | &quot; ++ (if(statusFlag) &quot;NOTIFICATION&quot; else &quot;ERROR&quot;) ++  &quot; | &quot; ++ p('app.name') ++ &quot; | &quot; ++ (if(statusFlag) &quot;SupplierREach Processed Successfully&quot; else &quot;SupplierREach Not Processed&quot;) ++  &quot; | &quot; ++ (vars.vCorrelationId default &quot;&quot;)),&#10;    &quot;content&quot;: {&#10;        &quot;resource&quot;: p('app.name'),&#10;        &quot;priority&quot;: if(statusFlag) &quot;NOTIFICATION&quot; else &quot;ERROR&quot;,&#10;        &quot;recordId&quot;: payload.transactionDocumentation.recordId as String,&#10;        &quot;user&quot;: payload.transactionDocumentation.contactData.email,&#10;        &quot;recordName&quot;: payload.transactionDocumentation.accountData.companyName ++ '-'++ (payload.transactionDocumentation.recordId as String),&#10;        &quot;code&quot;: if(statusFlag) &quot;200&quot; else &quot;500&quot;,&#10;        &quot;messageType&quot;: if(statusFlag) &quot;SUCCESS&quot; else &quot;FAILURE&quot;,&#10;        &quot;description&quot;: {&#10;            &quot;sourceSystem&quot;: &quot;SUPPLIER_REACH&quot;,&#10;            &quot;destinationSystem&quot;: &quot;SALESFORCE&quot;,&#10;            &quot;operation&quot;: &quot;CREATE&quot;,&#10;            &quot;object&quot;: &quot;SUPPLIER_REACH_RFQ&quot;,&#10;            &quot;transactionId&quot;: vars.vTransactionId,&#10;            &quot;message&quot;: if(statusFlag) (vars.vUpdateApexResponse.processTransactionOutput.message default &quot;&quot;) else (vars.vUpdateApexResponse.processTransactionOutput.message default &quot;SupplierREach Not Processed&quot;),&#10;            &quot;description&quot;: (&#10;                if(statusFlag) &quot;Supplier REach has been saved in Salesforce&quot; else write(vars.vUpdateApexResponse.processTransactionOutput.details default &quot;&quot;)&#10;            )&#10;        },&#10;        &quot;correlationId&quot;: vars.vCorrelationId default &quot;&quot;,&#10;        &quot;timestamp&quot;: now() &gt;&gt; 'IST'&#10;    },&#10;    &quot;slackNotificationFlag&quot;: &quot;1&quot;&#10;}]" doc:name="vEmailDetails" doc:id="aa1f78df-a908-4b41-81a3-42d2422d2f8d" variableName="vEmailDetails" />
		<flow-ref doc:name="Flow Reference to sf-send-notification" doc:id="7ab17088-6140-41b5-9e76-f108bc8b86f1" name="sf-send-notification" />
		<ee:transform doc:name="Set payload, httpStatus" doc:id="ddfb8e75-9e5b-4092-8c49-272855ea39de">
			<ee:message>
				<ee:set-payload><![CDATA[%dw 2.0
output application/json
---
vars.vUpdateApexResponse.processTransactionOutput]]></ee:set-payload>
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="httpStatus" ><![CDATA[200]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<logger level="DEBUG" doc:name="LOG DEBUG: Outbound Response Payload" doc:id="05bce454-a41b-4711-a286-4a28d9fb7a00" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Log Outbound Response Payload", &#10;	"FlowName" : "pf-supplierREach-to-salesforce", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/supplier-reach/rfq",&#10;	"SFResponse": payload,&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Outbound Response" doc:id="3674c195-5b66-41b5-8993-4e8ea996f0b2" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Response",&#10;	"FlowName" : "pf-supplierREach-to-salesforce",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/supplier-reach/rfq",&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit" doc:id="79d06523-42bf-4d7e-b395-1110e425d942" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "SupplierREach process flow ended", &#10;	"FlowName" : "pf-supplierREach-to-salesforce", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		
	</flow>
	
</mule>