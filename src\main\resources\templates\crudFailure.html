<html>

<head>
    <style>
        th, td {
            padding: 6px;
        }
        .WARN {
            /* background-color: rgb(255, 255, 0); */
            background-color: white;
        }
        .ERROR {
            /* background-color: rgb(255, 50, 50); */
            background-color: white;
        }
        .INFO {
            /* background-color: rgb(153, 255, 0); */
            background-color: white;
        }
    </style>
</head>

<body>
	<p>You are receiving this email because an error occured in #[app.name]</p>
	</br></br>
    <table style="width:100%" border="1">
        <tr style="padding: 10px;">
            <th bgcolor="lightblue">RecordId</th>
            <th bgcolor="lightblue">RecordName</th>
            <th bgcolor="lightblue">User</th>
            <th bgcolor="lightblue">StatusCode</th>
            <th bgcolor="lightblue">MessageType</th>
			<th bgcolor="lightblue">Object</th>
			<th bgcolor="lightblue">Source</th>
			<th bgcolor="lightblue">Target</th>
			<th bgcolor="lightblue">Operation</th>
            <th bgcolor="lightblue">TransactionId</th>
            <th bgcolor="lightblue">Content</th>
            <th bgcolor="lightblue">Description</th>
        </tr>
        #[
	        output text/plain
	        ---
            (
                "<tr style=\"text-align: center;\" class=\"" ++ vars.vEmailDetails.content.priority ++ "\">" ++
                    "<td>#[vars.vEmailDetails.content.recordId]</td>" ++
                    "<td>#[vars.vEmailDetails.content.recordName]</td>" ++
                    "<td>#[vars.vEmailDetails.content.user]</td>" ++ 
                    "<td>#[vars.vEmailDetails.content.code]</td>" ++ 
                    "<td>#[vars.vEmailDetails.content.messageType]</td>" ++ 
                    "<td>#[vars.vEmailDetails.content.description.object]</td>" ++ 
                    "<td>#[vars.vEmailDetails.content.description.sourceSystem]</td>" ++
                    "<td>#[vars.vEmailDetails.content.description.destinationSystem]</td>" ++
                    "<td>#[vars.vEmailDetails.content.description.operation]</td>" ++
                    "<td>#[vars.vEmailDetails.content.description.transactionId]</td>" ++
                    "<td>#[vars.vEmailDetails.content.description.message]</td>" ++
                    "<td>" ++ (vars.vEmailDetails.content.description.description default "") ++ "</td>" ++
                "</tr>"
            )
		]
    </table>
</body>

</html>