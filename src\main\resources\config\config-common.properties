### HTTPS Listener ###
https.listener.host=0.0.0.0
https.listener.port=8081
https.listener.readTimeout=30000
https.listener.idleTimeout=30000

### Error definition ###
errorCodeMessage.apikit.badRequest.code=400
errorCodeMessage.apikit.badRequest.description=Bad Request
errorCodeMessage.apikit.notFound.code=404
errorCodeMessage.apikit.notFound.description=Not Found
errorCodeMessage.apikit.methodNotAllowed.code=405
errorCodeMessage.apikit.methodNotAllowed.description=Method Not Allowed
errorCodeMessage.apikit.notAcceptable.code=406
errorCodeMessage.apikit.notAcceptable.description=Not Acceptable
errorCodeMessage.apikit.unsupportedMediaType.code=415
errorCodeMessage.apikit.unsupportedMediaType.description=Unsupported Media Type
errorCodeMessage.apikit.notImplemented.code=501
errorCodeMessage.apikit.notImplemented.description=Not Implemented

### DB SYS API ###
#https.request.dbSysApi.transactionDetails.path=/api/TRANSACTION


### DB SYS API ###
https.request.dbSysApi.refId.path=/api/REF_ID
https.request.dbSysApi.transactionDetails.path=/api/TRANSACTION
https.request.dbSysApi.transactionTaskDetails.path=/api/TRANSACTION_TASK
https.request.dbSysApi.configLookup.path=/api/CONFIG_LOOKUP

### SyncPrc API's endpoints###
https.request.syncPrcApi.syncRecords.path=/api/syncRecords
https.request.sysApi.counterParty.path=/api/counterParty