<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:db="http://www.mulesoft.org/schema/mule/db"
	xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd">
 
	<flow name="pf-on-dp-counterParty-created"
		doc:id="44e29b3c-fd65-4350-a77a-ea4e0e2880fd" maxConcurrency="1">
		<logger level="INFO" doc:name="LOG INFO: Log Entry"
			doc:id="99685ec4-fe0a-4402-8938-ca70335357dc"
			message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-on-dp-counterParty-created", &#10;	"CorrelationID" : vars.vOrigCorrelationId&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request"
			doc:id="0b62fe3f-b0d3-4fdd-93ce-72d39c9b12e4"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "pf-on-dp-counterParty-created",&#10;	"CorrelationID" : vars.vOrigCorrelationId,&#10;	"Endpoint": "/api/CPCounterparty"&#10;}]' />
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Request Payload"
			doc:id="2493e172-f85c-40a4-9a58-b628e66c8434"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "pf-on-dp-counterParty-created",&#10;	"CorrelationID" : vars.vOrigCorrelationId,&#10;	"Endpoint": "/api/CPCounterparty",&#10;	"BackendRequest": payload&#10;}]' />
		<set-variable value="#[output application/json --- []]" doc:name="vFinalResponse" doc:id="a1449f55-13c3-40a7-bafe-c5ff8d8cd7fa" variableName="vFinalResponse" />
		<ee:transform doc:name="Transform Message" doc:id="a752ff52-a915-46ca-af14-eed8ad793298">
					<ee:message>
					</ee:message>
					<ee:variables>
						<ee:set-variable variableName="vArtemisRecord"><![CDATA[%dw 2.0
output application/json
---
{
    "sourceSystem": "DIGITALPRODUCTS",
    "transactionID": vars.vTransactionId,
    "requestMethod": "SQL",
    "requestAction": "CPSaveCounterparty",
    "updatedBy": payload.company.updatedBy,
    "sync": "synchronous",
    "payload": {
        "company": payload.company
    }
}]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
		<try doc:name="Try" doc:id="5087d239-09ec-4405-8851-210c2fdf0881">
				<http:request method="POST" doc:name="Call artemis-sys-api to create Counterparty" doc:id="7eb07eb6-0876-4c75-bafe-a059ee8bf8af" config-ref="HTTPS_Request_Artemis_SYS_API" path="#[p('https.request.sysApi.counterParty.path')]">
					<http:body><![CDATA[#[vars.vArtemisRecord]]]></http:body>
					<http:headers><![CDATA[#[output application/java
---
{
	"x-source": vars.vSource,
	"x-transactionId": vars.vTransactionId,
	"x-msg-timestamp": now() as String,
	"sourceId": vars.vSource,
	"destinationId": "ARTEMIS",
	"context": (vars.vCreateContext default "REGULAR")
}]]]></http:headers>
				</http:request>
					<error-handler>
					<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error propagate" doc:id="********-d2ee-4c23-836b-47a8bfaccee7" type="ANY">
						<logger level="WARN" doc:name="LOG WARN: Errorneous record" doc:id="11b2c0a4-a07b-45c5-bb9b-b62d5919b51f" message='#[output application/json&#10;---&#10;{&#10;	"message": "Error while inserting record into Database",&#10;	"error": error.description	&#10;}]' />
<!-- [STUDIO:"Update vFinalResponse"]						<set-variable value="#[output application/json&#10;&#45;&#45;-&#10;vars.vFinalResponse + ({&#10;	&quot;accountId&quot;: payload.account.'accountId' default &quot;-1&quot;,&#10;	&quot;eid&quot;: vars.vInsertRefIdResponse.response.'ENTERPRISE_ID' default &quot;-1&quot;,&#10;	&quot;flag&quot;: false&#10;})&#93;" doc:name="Update vFinalResponse" doc:id="69b0208c-d6f4-400d-b862-cab708514c59" variableName="vFinalResponse" /> [STUDIO] -->
					</on-error-propagate>
				</error-handler>
			</try>
		<ee:transform doc:name="Set payload, httpStatus" doc:id="3254808d-99ef-47b5-8429-957fe03b7384">
					<ee:message>
						<ee:set-payload><![CDATA[output application/json
---
payload]]></ee:set-payload>
					</ee:message>
					<ee:variables>
						<ee:set-variable variableName="httpStatus"><![CDATA[output application/json
---
if(!isEmpty(payload.code)) 201 else 400]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Response Payload"
			doc:id="91c9d9d5-5699-4f17-915f-013acaae6d2c"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-on-dp-counterParty-created",&#10;	"CorrelationID" : vars.vOrigCorrelationId,&#10;	"Endpoint": "/api/CPCounterparty",&#10;	"BackendResponse": payload&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response"
			doc:id="301732e2-8e54-4118-9ae1-afe47dbf2a5d"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-on-dp-counterParty-created",&#10;	"CorrelationID": vars.vOrigCorrelationId,&#10;	"Endpoint": "/api/CPCounterparty"&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit"
			doc:id="37d64935-206d-49e4-9d95-b12fd5d090fc"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-on-dp-counterParty-created",&#10;	"CorrelationID": vars.vOrigCorrelationId&#10;}]' />
	</flow>
</mule>