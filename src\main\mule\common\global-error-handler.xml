<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:os="http://www.mulesoft.org/schema/mule/os" xmlns:cloudhub="http://www.mulesoft.org/schema/mule/cloudhub"
    xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core" xmlns:tls="http://www.mulesoft.org/schema/mule/tls"
    xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns="http://www.mulesoft.org/schema/mule/core"
    xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/tls http://www.mulesoft.org/schema/mule/tls/current/mule-tls.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/cloudhub http://www.mulesoft.org/schema/mule/cloudhub/current/mule-cloudhub.xsd
http://www.mulesoft.org/schema/mule/os http://www.mulesoft.org/schema/mule/os/current/mule-os.xsd">

    <error-handler name="global-error-handler">
        <!-- <on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate"
            doc:id="01d6214e-2cb2-4faa-8838-b3cc3442d1c4" type="RECORD_DEPENDENCY:CAN_NOT_DELETE">            
            <set-variable value='#[attributes.statusCode default "500"]' doc:name="httpStatus"
                doc:id="750a0956-6e2c-48be-81cf-9f3c13d53913" variableName="httpStatus" />
            <set-variable
                value='#[{&#10;&#10;	message: "CAN_NOT_DELETE",&#10;&#10;	details: error.description default ""&#10;&#10;}]'
                doc:name="errorDescription" doc:id="ff2bf92f-2b17-4938-ac6a-3a717a41098a" variableName="errorDescription" />
            <flow-ref doc:name="common-error-sub-flow" doc:id="31e966bd-0da2-4e28-968d-157764c4d06f" name="common-error-sub-flow" />
        </on-error-propagate> -->
		
		<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate"
            doc:id="7fbb8b8e-9718-44b2-9b61-c5efa0060e85" type="SALESFORCE:CONNECTIVITY, SALESFORCE:INVALID_INPUT">            
            <set-variable value='#["500"]' doc:name="httpStatus"
                doc:id="ebb26c0f-1af6-4088-a29d-0dbe49a18cae" variableName="httpStatus" />
            <set-variable
                value="#[output application/json&#10;---&#10;{&#10;	message: &quot;ENDSYSTEM_CONNECTIVITY_FAILED&quot;,&#10;&#10;	details: error.description,&#10;&#10;	(&quot;detailedDescription&quot;: error.'errorMessage'.'payload') if(!isEmpty(error.'errorMessage'.'payload'))&#10;}]"
                doc:name="errorDescription" doc:id="bcef117b-458f-4bd7-8020-268e37ddafa8" variableName="errorDescription" />
			<http:request method="PUT" doc:name="Update TRANSACTION_DETAILS" doc:id="7bbeb9ef-18e9-48cd-853a-3f3af45dd6ef" config-ref="HTTPS_Request_DB_SYS_API" path="${https.request.dbSysApi.transactionDetails.path}" target="vTransactionResponse">
				<http:body><![CDATA[#[output application/json
---
{
  "transaction": {
    "STATUS": "FAILED",
    "ERROR_TYPE": "SYSTEM",
    "ERROR_MSG": (vars.errorDescription.message default "") ++ " | " ++ (vars.errorDescription.details default vars.errorDescription.detailedDescription default ""),
    "LAST_UPDATED_BY": "PROCESS_API"
  }
}]]]></http:body>
				<http:query-params ><![CDATA[#[output application/java
---
{
	"CORRELATION_ID": vars.vCorrelationId
}]]]></http:query-params>
			</http:request>
			<set-variable value="#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	&quot;fromAddress&quot;: p('smtp.user'),&#10;	&quot;toAddresses&quot;: p('smtp.systemFailure.toAddresses') splitBy &quot;,&quot;,&#10;	&quot;ccAddresses&quot;: [],&#10;	&quot;subject&quot;: (upper(p('mule.env')) ++ &quot; | ERROR | &quot; ++ p('app.name') ++ &quot; | ENDSYSTEM_CONNECTIVITY_FAILED | &quot; ++ (vars.vCorrelationId default &quot;&quot;)), &#10;	&quot;content&quot;: {&#10;		&quot;resource&quot;: p('app.name'),&#10;		&quot;priority&quot;: &quot;ERROR&quot;,&#10;		&quot;recordId&quot;: &quot;&quot;,&#10;		&quot;user&quot;: &quot;&quot;,&#10;		&quot;recordName&quot;: &quot;&quot;,&#10;		&quot;code&quot;: vars.httpStatus default &quot;500&quot;,&#10;		&quot;messageType&quot;: vars.errorDescription.message,&#10;		&quot;description&quot;: {&#10;			&quot;sourceSystem&quot;: &quot;MARKETPLACE&quot;,&#10;			&quot;destinationSystem&quot;: &quot;SALESFORCE&quot;,&#10;			&quot;operation&quot;: &quot;CREATE&quot;,&#10;			&quot;object&quot;: &quot;DEAL&quot;,&#10;			&quot;transactionId&quot;: vars.vTransactionId,&#10;			&quot;message&quot;: vars.errorDescription.message,&#10;			&quot;description&quot;: vars.errorDescription.details&#10;		},&#10;		&quot;correlationId&quot;: vars.vCorrelationId default &quot;&quot;,&#10;		&quot;timestamp&quot;: now() &gt;&gt; 'IST'&#10;	},&#10;	&quot;slackNotificationFlag&quot;: &quot;1&quot;&#10;}]" doc:name="vEmailDetails" doc:id="132cc967-c942-47a0-b378-cdb19f97c113" variableName="vEmailDetails" />
			<flow-ref doc:name="Flow Reference to sf-send-notification" doc:id="b28d33f7-77bc-4878-b518-5cbfeffd92de" name="sf-send-notification" />
			<flow-ref doc:name="common-error-sub-flow" doc:id="dda67cae-787d-45b3-8751-121feb651d03" name="common-error-sub-flow" />
        </on-error-propagate>
        <on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate"
            doc:id="95c1a2f9-b974-4889-a11c-8dc6adb0260e" type="SALESFORCE:INVALID_RESPONSE, SALESFORCE:NOT_FOUND, SALESFORCE:RETRY_EXHAUSTED, SALESFORCE:TIMEOUT">
            <set-variable value='#["400"]' doc:name="httpStatus" doc:id="bc002062-4963-4e44-aeef-1cfd535d1e9d" variableName="httpStatus" />
		<set-variable value="#[output application/json&#10;---&#10;{&#10;	message: &quot;SALESFORCE ERROR&quot;,&#10;&#10;	details: error.description,&#10;&#10;	(&quot;detailedDescription&quot;: error.'errorMessage'.'payload') if(!isEmpty(error.'errorMessage'.'payload'))&#10;}]" doc:name="errorDescription" doc:id="fbc8062c-4fcb-41d0-bc54-e516b93be05a" variableName="errorDescription" />
		<http:request method="PUT" doc:name="Update TRANSACTION_DETAILS" doc:id="99b4653e-d486-4e76-a51a-230594e4e458" config-ref="HTTPS_Request_DB_SYS_API" path="${https.request.dbSysApi.transactionDetails.path}" target="vTransactionResponse">
				<http:body><![CDATA[#[output application/json
---
{
  "transaction": {
    "STATUS": "FAILED",
    "ERROR_TYPE": "DATA",
    "ERROR_MSG": (vars.errorDescription.message default "") ++ " | " ++ (vars.errorDescription.details default vars.errorDescription.detailedDescription default ""),
    "LAST_UPDATED_BY": "PROCESS_API"
  }
}]]]></http:body>
				<http:query-params><![CDATA[#[output application/java
---
{
	"CORRELATION_ID": vars.vCorrelationId
}]]]></http:query-params>
			</http:request>
		<set-variable value="#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	&quot;fromAddress&quot;: p('smtp.user'),&#10;	&quot;toAddresses&quot;: p('smtp.dataFailure.toAddresses') splitBy &quot;,&quot;,&#10;	&quot;ccAddresses&quot;: [],&#10;	&quot;subject&quot;: (upper(p('mule.env')) ++ &quot; | ERROR | &quot; ++ p('app.name') ++ &quot; | SALESFORCE ERROR | &quot; ++ (vars.vCorrelationId default &quot;&quot;)), &#10;	&quot;content&quot;: {&#10;		&quot;resource&quot;: p('app.name'),&#10;		&quot;priority&quot;: &quot;ERROR&quot;,&#10;		&quot;recordId&quot;: &quot;&quot;,&#10;		&quot;user&quot;: &quot;&quot;,&#10;		&quot;recordName&quot;: &quot;&quot;,&#10;		&quot;code&quot;: vars.httpStatus default &quot;500&quot;,&#10;		&quot;messageType&quot;: vars.errorDescription.message,&#10;		&quot;description&quot;: {&#10;			&quot;sourceSystem&quot;: &quot;MARKETPLACE&quot;,&#10;			&quot;destinationSystem&quot;: &quot;SALESFORCE&quot;,&#10;			&quot;operation&quot;: &quot;CREATE&quot;,&#10;			&quot;object&quot;: &quot;DEAL&quot;,&#10;			&quot;transactionId&quot;: vars.vTransactionId,&#10;			&quot;message&quot;: vars.errorDescription.message,&#10;			&quot;description&quot;: vars.errorDescription.details&#10;		},&#10;		&quot;correlationId&quot;: vars.vCorrelationId default &quot;&quot;,&#10;		&quot;timestamp&quot;: now() &gt;&gt; 'IST'&#10;	},&#10;	&quot;slackNotificationFlag&quot;: &quot;1&quot;&#10;}]" doc:name="vEmailDetails" doc:id="9909521b-b767-45d2-9692-2f7ed845a094" variableName="vEmailDetails" />
		<flow-ref doc:name="Flow Reference to sf-send-notification" doc:id="980f7589-8cea-4e2b-8f5b-7d3a5e0705be" name="sf-send-notification" />
		<flow-ref doc:name="common-error-sub-flow" doc:id="92c913e6-eaa9-4794-9702-ad307ab1e049" name="common-error-sub-flow" />
	
        </on-error-propagate>
        <on-error-propagate type="APIKIT:BAD_REQUEST">
			<set-variable value='#[attributes.statusCode default "400"]' doc:name="httpStatus" doc:id="702f548b-949a-41c9-a6da-9129fb028082" variableName="httpStatus" />
			<set-variable value="#[output application/json&#10;---&#10;{&#10;	message: &quot;BAD_REQUEST&quot;,&#10;&#10;	details: error.description,&#10;&#10;	(&quot;detailedDescription&quot;: error.'errorMessage'.'payload') if(!isEmpty(error.'errorMessage'.'payload'))&#10;}]" doc:name="errorDescription" doc:id="4595c2c8-08e0-40fa-92d0-a7994b74c380" variableName="errorDescription" />
			<flow-ref doc:name="common-error-sub-flow" doc:id="6893ad2d-a979-46c6-915e-66f58667b38c" name="common-error-sub-flow" />
            </on-error-propagate>
            <on-error-propagate type="APIKIT:NOT_FOUND">
			<set-variable value='#[attributes.statusCode default "404"]' doc:name="httpStatus" doc:id="03f18326-1729-45d8-873b-e2b024e5f4d7" variableName="httpStatus" />
			<set-variable value="#[output application/json&#10;---&#10;{&#10;	message: &quot;NOT_FOUND&quot;,&#10;&#10;	details: error.description,&#10;&#10;	(&quot;detailedDescription&quot;: error.'errorMessage'.'payload') if(!isEmpty(error.'errorMessage'.'payload'))&#10;}]" doc:name="errorDescription" doc:id="263dc08a-3adc-4963-a09d-f8d34f7ee7b7" variableName="errorDescription" />
			<flow-ref doc:name="common-error-sub-flow" doc:id="0879e7e9-24f5-4e0e-95f8-10c7ae6bb078" name="common-error-sub-flow" />
            </on-error-propagate>
            <on-error-propagate type="APIKIT:METHOD_NOT_ALLOWED">
			<set-variable value='#[attributes.statusCode default "405"]' doc:name="httpStatus" doc:id="e5cfdccb-66f6-463f-861d-b8fdc0e09ba0" variableName="httpStatus" />
			<set-variable value="#[output application/json&#10;---&#10;{&#10;	message: &quot;METHOD_NOT_ALLOWED&quot;,&#10;&#10;	details: error.description,&#10;&#10;	(&quot;detailedDescription&quot;: error.'errorMessage'.'payload') if(!isEmpty(error.'errorMessage'.'payload'))&#10;}]" doc:name="errorDescription" doc:id="70d84997-718b-43d2-949b-0889c47ed1cc" variableName="errorDescription" />
			<flow-ref doc:name="common-error-sub-flow" doc:id="cf254e54-0735-435d-9bf2-96f02e5e3f9a" name="common-error-sub-flow" />
            </on-error-propagate>
            <on-error-propagate type="APIKIT:NOT_ACCEPTABLE">
			<set-variable value='#[attributes.statusCode default "406"]' doc:name="httpStatus" doc:id="ed4949c4-168c-4c0a-b117-078df9c85349" variableName="httpStatus" />
			<set-variable value="#[output application/json&#10;---&#10;{&#10;	message: &quot;NOT_ACCEPTABLE&quot;,&#10;&#10;	details: error.description,&#10;&#10;	(&quot;detailedDescription&quot;: error.'errorMessage'.'payload') if(!isEmpty(error.'errorMessage'.'payload'))&#10;}]" doc:name="errorDescription" doc:id="f373caf3-13aa-406c-9776-7f45afa8c9fa" variableName="errorDescription" />
			<flow-ref doc:name="common-error-sub-flow" doc:id="b2861214-17c8-4cb1-943f-4a5531385e3f" name="common-error-sub-flow" />
            </on-error-propagate>
            <on-error-propagate type="APIKIT:UNSUPPORTED_MEDIA_TYPE">
			<set-variable value='#[attributes.statusCode default "415"]' doc:name="httpStatus" doc:id="eee1f39e-a6e2-4e33-a368-4e496b6fc12b" variableName="httpStatus" />
			<set-variable value="#[output application/json&#10;---&#10;{&#10;	message: &quot;UNSUPPORTED_MEDIA_TYPE&quot;,&#10;&#10;	details: error.description,&#10;&#10;	(&quot;detailedDescription&quot;: error.'errorMessage'.'payload') if(!isEmpty(error.'errorMessage'.'payload'))&#10;}]" doc:name="errorDescription" doc:id="41ee38c3-f048-4e02-a52b-3d024f3039ac" variableName="errorDescription" />
			<flow-ref doc:name="common-error-sub-flow" doc:id="7dead34a-ad64-4fcc-a214-90f08d5740a9" name="common-error-sub-flow" />
            </on-error-propagate>
            <on-error-propagate type="APIKIT:NOT_IMPLEMENTED">
			<set-variable value='#[attributes.statusCode default "501"]' doc:name="httpStatus" doc:id="7c43861d-3180-47c7-a354-29561950fefd" variableName="httpStatus" />
			<set-variable value="#[output application/json&#10;---&#10;{&#10;	message: &quot;NOT_IMPLEMENTED&quot;,&#10;&#10;	details: error.description,&#10;&#10;	(&quot;detailedDescription&quot;: error.'errorMessage'.'payload') if(!isEmpty(error.'errorMessage'.'payload'))&#10;}]" doc:name="errorDescription" doc:id="13647498-1733-40f7-b993-e52cbc1da5af" variableName="errorDescription" />
			<flow-ref doc:name="common-error-sub-flow" doc:id="6f7af352-fbf7-4276-bc2f-94833626837f" name="common-error-sub-flow" />
        </on-error-propagate>
        
        <on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate"
            doc:id="521436df-692c-48ae-8c9f-8aec14735b9e" type="EXPRESSION">            
            <set-variable value='#[attributes.statusCode default "500"]' doc:name="httpStatus"
                doc:id="98c914fa-bba4-4d54-b278-5a05ff14292b" variableName="httpStatus" />
            <set-variable
                value="#[output application/json&#10;---&#10;{&#10;	message: &quot;DATAWEAVE_EXPRESSION_ERROR&quot;,&#10;	details: error.description default &quot;&quot;,&#10;	(&quot;detailedDescription&quot;: error.'errorMessage'.'payload') if(!isEmpty(error.'errorMessage'.'payload'))&#10;}]"
                doc:name="errorDescription" doc:id="4475c8dd-5c8a-47c5-9e07-2345e8f08032" variableName="errorDescription" />
			<http:request method="PUT" doc:name="Update TRANSACTION_DETAILS" doc:id="7bba1f48-c13d-4742-ad47-ac9487fae958" config-ref="HTTPS_Request_DB_SYS_API" path="${https.request.dbSysApi.transactionDetails.path}" target="vTransactionResponse">
				<http:body><![CDATA[#[output application/json
---
{
  "transaction": {
    "STATUS": "FAILED",
    "ERROR_TYPE": "SYSTEM",
    "ERROR_MSG": (vars.errorDescription.message default "") ++ " | " ++ (vars.errorDescription.details default vars.errorDescription.detailedDescription default ""),
    "LAST_UPDATED_BY": "PROCESS_API"
  }
}]]]></http:body>
				<http:query-params ><![CDATA[#[output application/java
---
{
	"CORRELATION_ID": vars.vCorrelationId
}]]]></http:query-params>
			</http:request>
			<flow-ref doc:name="common-error-sub-flow" doc:id="6e86a83d-5750-4bfe-a112-f05ae3247bad" name="common-error-sub-flow" />
        
</on-error-propagate>
        <on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="27fcef22-cc88-4fa9-becd-6169dc5f419f" type="ANY">            
            <set-variable value='#[if(!isEmpty(attributes.statusCode)) attributes.statusCode else if(!isEmpty(error.muleMessage.typedValue.code)) (error.muleMessage.typedValue.code) else "500"]' doc:name="httpStatus" doc:id="443ad850-341a-45df-b72d-98acc04d2b6a" variableName="httpStatus" />
            <set-variable value="#[output application/json&#10;---&#10;{&#10;	message: &quot;INTERNAL_SERVER_ERROR&quot;,&#10;	details: error.description default &quot;&quot;,&#10;	(&quot;detailedDescription&quot;: error.'errorMessage'.'payload') if(!isEmpty(error.'errorMessage'.'payload'))&#10;}]" doc:name="errorDescription" doc:id="6ba93270-6a90-4544-9fbe-e156b2d69c9e" variableName="errorDescription" />
			<http:request method="PUT" doc:name="Update TRANSACTION_DETAILS" doc:id="aa557613-7301-4c53-aa5f-45fe60429c29" config-ref="HTTPS_Request_DB_SYS_API" path="${https.request.dbSysApi.transactionDetails.path}" target="vTransactionResponse">
				<http:body><![CDATA[#[output application/json
---
{
  "transaction": {
    "STATUS": "FAILED",
    "ERROR_TYPE": "SYSTEM",
    "ERROR_MSG": write((vars.errorDescription default ""), "application/json"),
    "LAST_UPDATED_BY": "PROCESS_API"
  }
}]]]></http:body>
				<http:query-params><![CDATA[#[output application/java
---
{
	"CORRELATION_ID": vars.vCorrelationId
}]]]></http:query-params>
			</http:request>
			<flow-ref doc:name="common-error-sub-flow" doc:id="8f7988e5-29ed-43a1-90a9-52a32513b6f1" name="common-error-sub-flow" />
        
</on-error-propagate>
    </error-handler>
	<sub-flow name="common-error-sub-flow" doc:id="588ae881-c4ee-46e0-9173-7251a11ce22d">
		<set-payload
            value='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	code: vars.httpStatus,&#10;	status: "FAILURE",&#10;	transactionId: vars.vCorrelationId,&#10;	response: {&#10;		message: vars.errorDescription.message,&#10;		details: vars.errorDescription.details,&#10;		detailedDescription: vars.errorDescription.detailedDescription&#10;	}&#10;}]'
            doc:name="Final Error Response" doc:id="5dadb469-5c4d-48b3-8862-74c34e91c61e" />
        <logger level="ERROR" doc:name="Exit Generic Error Handler" doc:id="40d6d840-d6f9-4414-a4aa-5cb3d37ff6c2"
            message='#[payload]' />
    
</sub-flow>
	
</mule>
