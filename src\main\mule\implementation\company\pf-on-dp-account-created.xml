<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:db="http://www.mulesoft.org/schema/mule/db"
	xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd">
 
	<flow name="pf-on-dp-account-created"
		doc:id="44e29b3c-fd65-4350-a77a-ea4e0e2880fd" maxConcurrency="1">
		<logger level="INFO" doc:name="LOG INFO: Log Entry"
			doc:id="f7bf7a75-0f24-47cf-9d6d-85a58fd1b0d2"
			message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-on-dp-account-created", &#10;	"CorrelationID" : vars.vOrigCorrelationId&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request"
			doc:id="21a923cf-9ec4-448c-8517-257f2072c472"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "pf-on-dp-account-created",&#10;	"CorrelationID" : vars.vOrigCorrelationId,&#10;	"Endpoint": "/api/account"&#10;}]' />
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Request Payload"
			doc:id="64b9ea0a-f6de-44fa-940b-db277e25782a"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "pf-on-dp-account-created",&#10;	"CorrelationID" : vars.vOrigCorrelationId,&#10;	"Endpoint": "/api/company",&#10;	"BackendRequest": payload&#10;}]' />
		<choice doc:name="Check enableSyncForCP" doc:id="171a4a25-9d68-49ad-9a35-105c96081065" >
			<when expression="#[Mule::p('enableSyncForCP') ~= &quot;1&quot;]">
				<logger level="INFO" doc:name="LOG INFO: enableSyncForDP is enabled" doc:id="72c1091a-26ca-4b0c-9ffa-538309dc9d50" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Continue record sync as enableSyncForDP is enabled", &#10;	"FlowName" : "pf-on-dp-account-created", &#10;	"CorrelationID" : vars.vOrigCorrelationId&#10;}]'/>
				<set-variable value="#[output application/json --- []]" doc:name="vFinalResponse" doc:id="6fedd92e-4036-4168-b740-fa8047723bf0" variableName="vFinalResponse" />
				
				<try doc:name="Try" doc:id="9b84bc55-5f3a-450e-944e-c61f454ce6ab">
				<set-variable value="#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	&quot;headers&quot;: {&#10;		&quot;x-source&quot;: &quot;Digital Products&quot;,&#10;		&quot;x-transactionId&quot;: vars.vTransactionId,&#10;		//&quot;x-msg-timestamp&quot;: (now() as LocalDateTime {format: &quot;yyyy-MM-dd'T'HH:mm:ss.000'Z'&quot;}),&#10;		&quot;correlationId&quot;: vars.vCorrelationId,&#10;		&quot;sourceId&quot;: &quot;3degrees-mktplace-sf-api&quot;,&#10;		&quot;destinationId&quot;: &quot;TRANSACTION_DB_SYS_API&quot;,&#10;		&quot;content-type&quot;: &quot;application/json&quot;&#10;	}&#10;}]" doc:name="vRequestAttributes" doc:id="90d9e1db-aba4-4d44-917e-117d3ed6f611" variableName="vRequestAttributes" />
				<flow-ref doc:name="Flow Reference to sf-insert-record" doc:id="b59a38f1-ccb1-4a67-bcce-7c2d6ccc8943" name="sf-insert-record" />
				<error-handler>
					<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="24fadc57-c488-4ad8-8ff9-183dc5b141f8" type="ANY">
						<logger level="WARN" doc:name="LOG WARN: Errorneous record" doc:id="fab719b8-5832-4990-b6d2-7ddcb0158233" message='#[output application/json&#10;---&#10;{&#10;	"message": "Error while inserting record into Database",&#10;	"error": error.description	&#10;}]' />
						<set-variable value="#[output application/json&#10;---&#10;vars.vFinalResponse + ({&#10;	&quot;accountId&quot;: payload.account.'accountId' default &quot;-1&quot;,&#10;	&quot;eid&quot;: vars.vInsertRefIdResponse.response.'ENTERPRISE_ID' default &quot;-1&quot;,&#10;	&quot;flag&quot;: false&#10;})]" doc:name="Update vFinalResponse" doc:id="7d439399-5043-4d8c-9837-953a9396f6c5" variableName="vFinalResponse" />
					</on-error-continue>
				</error-handler>
			</try>
		
				<ee:transform doc:name="Set payload, httpStatus" doc:id="b8a15e2c-26f4-4c8d-a031-9756c32cf400" >
					<ee:message >
						<ee:set-payload ><![CDATA[output application/json
---
{
  "code": if(!isEmpty(vars.vFinalResponse filter(($).'flag' ~= true))) 201 else 400,
  "transactionId": vars.vTransactionId,
  "status": if(!isEmpty(vars.vFinalResponse filter(($).'flag' ~= true))) "SUCCESS" else "FAILURE",
  "response": vars.vFinalResponse
}]]></ee:set-payload>
					</ee:message>
					<ee:variables >
						<ee:set-variable variableName="httpStatus" ><![CDATA[output application/json
---
if(!isEmpty(vars.vFinalResponse filter(($).'flag' ~= true))) 201 else 400]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
			</when>
			<otherwise >
				<logger level="INFO" doc:name="LOG INFO: enableSyncForArtemis is disabled" doc:id="feb83334-eb42-4a1d-babd-4e62ce4acf94" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Discard record sync as enableSyncForCP is disabled", &#10;	"FlowName" : "pf-on-cp-account-created", &#10;	"CorrelationID" : vars.vOrigCorrelationId&#10;}]'/>
				<ee:transform doc:name="Set payload, httpStatus" doc:id="9f566d90-064a-4c16-b530-b4ac53711665">
					<ee:message>
						<ee:set-payload><![CDATA[output application/json
---
{
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "IGNORED",
  "response": {
  	"message": "Record will not be processed for syncing",
  	"description": "Syncing is ignored as enableSyncForArtemis is disabled"
  }
}]]></ee:set-payload>
					</ee:message>
					<ee:variables>
						<ee:set-variable variableName="httpStatus"><![CDATA[200]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
			</otherwise>
		</choice>
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Response Payload"
			doc:id="19b158b5-5863-48f3-8b17-1da908cf5313"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-on-dp-account-created",&#10;	"CorrelationID" : vars.vOrigCorrelationId,&#10;	"Endpoint": "/api/company",&#10;	"BackendResponse": payload&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response"
			doc:id="bffd2370-4ada-4d8b-8aab-6600cd584f0b"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-on-dp-account-created",&#10;	"CorrelationID": vars.vOrigCorrelationId,&#10;	"Endpoint": "/api/account"&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit"
			doc:id="19209c96-1e74-4826-ae9d-5be4b647c1d7"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-on-dp-account-created",&#10;	"CorrelationID": vars.vOrigCorrelationId&#10;}]' />
	</flow>
	<sub-flow name="sf-insert-record"
		doc:id="2a0d60fe-feac-4b26-a5d0-2cba54b6f951">
		<choice doc:name="Check doNotSync" doc:id="d7b2e49b-84fd-4f3f-a8c1-5f83d1cff4b6" >
			<when expression="#[!(payload.'doNotSync' as Boolean default false)]">
				
				<http:request method="POST" doc:name="Insert into REF_ID" doc:id="b97251a9-99a5-40b8-bff5-7ef24c0df4ff" config-ref="HTTPS_Request_DB_SYS_API" path="/api/REF_ID" target="vInsertRefIdResponse">
					<http:body><![CDATA[#[%dw 2.0
output application/json skipNullOn='everywhere'
---
{
   "refID":{
     "OBJECT_TYPE": "ACCOUNT",
     "DIGITAL_PRODUCTS_ID": payload.company.id as String,
     "LAST_UPDATED_BY": "DIGITALPRODUCTS"
  }
}]]]></http:body>
					<http:headers ><![CDATA[#[output application/java
---
{
	"x-source": "Climate Portal",
		"x-transactionId": vars.vTransactionId,
		"correlationId": vars.vCorrelationId,
		"sourceId": "3degrees-mktplace-sf-api",
		"destinationId": "TRANSACTION_DB_SYS_API",
		"content-type": "application/json"
}]]]></http:headers>
				</http:request>
				<ee:transform doc:name="Set vTransactionDetailsRecord" doc:id="a40f3227-d916-4898-82f0-25c2fe175fff">
			<ee:message />
			<ee:variables>
				<ee:set-variable variableName="vTransactionDetailsRecord"><![CDATA[%dw 2.0
output application/json
---
{  
    "transaction": {
		"CORRELATION_ID": vars.vCorrelationId,
	    "OPERATION": "CREATE",
	    "SOURCE": "DIGITALPRODUCTS",
	    "STATUS": "QUEUED",
	    "LAST_UPDATED_BY": "EXPERIENCE_API",
	    "ENTERPRISE_ID":  vars.vInsertRefIdResponse.response.'ENTERPRISE_ID', 
	    "PAYLOAD": write({"account": payload}, 'application/json') default null,
	    "PRIORITY":(payload.syncPriority default "0") as Number,
	    "OBJECT_TYPE": "ACCOUNT",
	    "QUERY_PARAMS": null,
	    "RETRY_COUNT": 0,
	    "ERROR_MSG": null,
	    "ERROR_TYPE": null,
	    "USER": payload.company.accountUpdatedBy
	}
}]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
				<http:request method="POST" doc:name="Insert into TRANSACTION_DETAILS" doc:id="be4b84d0-3773-4513-bd6b-511bc42fba13" config-ref="HTTPS_Request_DB_SYS_API" path="#[p('https.request.dbSysApi.transactionDetails.path')]" target="vInsertTransactionResponse">
			<http:body><![CDATA[#[vars.vTransactionDetailsRecord]]]></http:body>
			<http:headers><![CDATA[#[output application/java
---
vars.vRequestAttributes.'headers' default ""]]]></http:headers>
		</http:request>
<!-- [STUDIO:"Call SyncPrc to process record"]				<http:request method="GET" doc:name="Call SyncPrc to process record" doc:id="9131c23f-51e9-419d-ace3-66bccbb0fb3d" config-ref="HTTPS_Request_Sync_Prc_API" path="#[p('https.request.syncPrcApi.syncRecords.path')&#93;" target="vSyncRecordResponse" >
					<http:body ><![CDATA[#[{}&#93;&#93;&#93;></http:body>
					<http:headers ><![CDATA[#[output application/java
&#45;&#45;-
{
	correlationId : vars.vCorrelationId
}&#93;&#93;&#93;></http:headers>
				</http:request> [STUDIO] -->
				<logger level="INFO" doc:name="LOG INFO: Sync record response" doc:id="********-69a2-49af-87c7-c9c387e43014" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Sync record response",&#10;	"FlowName" : "pf-on-dp-account-created",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"SyncRecordResponse": vars.vSyncRecordResponse&#10;}]' />
				<set-variable value="#[output application/json&#10;---&#10;vars.vFinalResponse + ({&#10;	&quot;accountId&quot;: payload.company.'id' default &quot;-1&quot;,&#10;	&quot;eid&quot;: vars.vInsertRefIdResponse.response.'ENTERPRISE_ID',&#10;	&quot;flag&quot;: if(vars.vInsertTransactionResponse.'status' ~= &quot;SUCCESS&quot;) true else false&#10;})]" doc:name="Update vFinalResponse" doc:id="de1c4fd9-35e4-40c9-a2f1-4298a902c168" variableName="vFinalResponse" />
			</when>
			<otherwise >
				<logger level="INFO" doc:name="LOG INFO: Discard sync" doc:id="20028b82-b379-4b56-952b-b7d0d212e39d" message="#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	&quot;Message&quot;: &quot;Record with CompanyId &quot; ++ payload.company.'id' ++ &quot; is ignored as doNotSync is true.&quot;,&#10;	&quot;FlowName&quot;: &quot;dp-insert-record&quot;,&#10;	&quot;CorrelationID&quot;: vars.vCorrelationId&#10;}]"/>
				<set-variable value='#[output application/json&#10;---&#10;vars.vFinalResponse + ({&#10;	"message": "Record will not be processed for syncing",&#10;	"description": "doNotSync is set to true",&#10;	"flag": false&#10;})]' doc:name="Update vFinalResponse" doc:id="78f3fb67-8f0c-4dee-a7fd-33f51060d683" variableName="vFinalResponse" />
			</otherwise>
		</choice>
	</sub-flow>
</mule>